Name:             gateway-c44865b56-sgrzf
Namespace:        openfaas
Priority:         0
Service Account:  openfaas-controller
Node:             thu-b560m-d2v/*************
Start Time:       Sat, 02 Aug 2025 16:32:59 +0800
Labels:           app=gateway
                  pod-template-hash=c44865b56
Annotations:      cni.projectcalico.org/containerID: 97229113778ceec4540829bc4d26b95f35bf3d1a8e2c6781c0a27c584b86d230
                  cni.projectcalico.org/podIP: **********/32
                  cni.projectcalico.org/podIPs: **********/32
                  kubectl.kubernetes.io/restartedAt: 2025-08-02T16:32:58+08:00
                  prometheus.io/port: 8082
                  prometheus.io/scrape: true
Status:           Running
IP:               **********
IPs:
  IP:           **********
Controlled By:  ReplicaSet/gateway-c44865b56
Containers:
  gateway:
    Container ID:  containerd://c2acd9248e858ea43514bbb2a5213e77dcb07868bc641176d0f872af64a0ddce
    Image:         ghcr.io/openfaas/gateway:0.27.12
    Image ID:      ghcr.io/openfaas/gateway@sha256:fe62d9e15c4d8743f8049d83f5c2f82a28bd12ede7dd66e5c51261161adb5980
    Ports:         8080/TCP, 8082/TCP
    Host Ports:    0/TCP, 0/TCP
    Command:
      ./gateway
    State:          Running
      Started:      Sat, 02 Aug 2025 17:25:19 +0800
    Last State:     Terminated
      Reason:       Error
      Exit Code:    2
      Started:      Sat, 02 Aug 2025 17:24:40 +0800
      Finished:     Sat, 02 Aug 2025 17:24:55 +0800
    Ready:          True
    Restart Count:  5
    Requests:
      cpu:      100m
      memory:   120Mi
    Liveness:   http-get http://:8080/healthz delay=1s timeout=5s period=5s #success=1 #failure=3
    Readiness:  http-get http://:8080/healthz delay=1s timeout=5s period=5s #success=1 #failure=3
    Environment:
      read_timeout:             1m05s
      write_timeout:            1m05s
      upstream_timeout:         1m
      functions_provider_url:   http://127.0.0.1:8081/
      direct_functions:         false
      direct_functions_suffix:  openfaas-fn.svc.cluster.local
      function_namespace:       openfaas-fn
      faas_nats_address:        nats.openfaas.svc.cluster.local
      faas_nats_port:           4222
      faas_nats_channel:        faas-request
      basic_auth:               true
      secret_mount_path:        /var/secrets
      scale_from_zero:          true
      max_idle_conns:           1024
      max_idle_conns_per_host:  1024
      probe_functions:          false
      async:                    true
    Mounts:
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-4bw2w (ro)
      /var/secrets from auth (ro)
  faas-netes:
    Container ID:  containerd://6ea56bc2f790ed27b508bc5bdfcf45817437dd68fabac3071e9ee61d8635efe5
    Image:         koinikki/faas-netes:latest
    Image ID:      docker.io/koinikki/faas-netes@sha256:812891807eee6320b80f1ac5ad85f6cd1e665bd91be828342039b831bc65c193
    Port:          8081/TCP
    Host Port:     0/TCP
    Command:
      ./faas-netes
    State:          Running
      Started:      Sat, 02 Aug 2025 17:23:58 +0800
    Ready:          True
    Restart Count:  2
    Requests:
      cpu:     100m
      memory:  120Mi
    Liveness:  http-get http://:8081/healthz delay=5s timeout=3s period=5s #success=1 #failure=3
    Environment:
      port:                                   8081
      function_namespace:                     openfaas-fn
      read_timeout:                           1m05s
      profiles_namespace:                     openfaas
      write_timeout:                          1m05s
      image_pull_policy:                      Always
      http_probe:                             true
      set_nonroot_user:                       false
      readiness_probe_initial_delay_seconds:  0
      readiness_probe_timeout_seconds:        1
      readiness_probe_period_seconds:         2
      readiness_probe_success_threshold:      1
      readiness_probe_failure_threshold:      3
      liveness_probe_initial_delay_seconds:   0
      liveness_probe_timeout_seconds:         1
      liveness_probe_period_seconds:          2
      liveness_probe_failure_threshold:       3
      cluster_role:                           false
      basic_auth:                             true
      secret_mount_path:                      /var/secrets
      debug:                                  false
      log_encoding:                           console
      http_proxy:                             http://192.168.0.182:7897
      https_proxy:                            http://192.168.0.182:7897
    Mounts:
      /tmp from faas-netes-temp-volume (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-4bw2w (ro)
      /var/secrets from auth (ro)
Conditions:
  Type                        Status
  PodReadyToStartContainers   True 
  Initialized                 True 
  Ready                       True 
  ContainersReady             True 
  PodScheduled                True 
Volumes:
  faas-netes-temp-volume:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     
    SizeLimit:  <unset>
  auth:
    Type:        Secret (a volume populated by a Secret)
    SecretName:  basic-auth
    Optional:    false
  kube-api-access-4bw2w:
    Type:                    Projected (a volume that contains injected data from multiple sources)
    TokenExpirationSeconds:  3607
    ConfigMapName:           kube-root-ca.crt
    Optional:                false
    DownwardAPI:             true
QoS Class:                   Burstable
Node-Selectors:              <none>
Tolerations:                 node.kubernetes.io/not-ready:NoExecute op=Exists for 300s
                             node.kubernetes.io/unreachable:NoExecute op=Exists for 300s
Events:                      <none>