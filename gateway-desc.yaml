Name:             gateway-64ccc4b899-n7gc8
Namespace:        openfaas
Priority:         0
Service Account:  openfaas-controller
Node:             thu-ms-7d48/*************
Start Time:       Mon, 04 Aug 2025 13:04:41 +0800
Labels:           app=gateway
                  pod-template-hash=64ccc4b899
Annotations:      cni.projectcalico.org/containerID: 109cbf5f6ea1b2853e8a85749994fc44bd99856a92a00f17a06a092be4ce5f12
                  cni.projectcalico.org/podIP: ***********/32
                  cni.projectcalico.org/podIPs: ***********/32
                  prometheus.io/port: 8082
                  prometheus.io/scrape: true
Status:           Running
IP:               ***********
IPs:
  IP:           ***********
Controlled By:  ReplicaSet/gateway-64ccc4b899
Containers:
  gateway:
    Container ID:  containerd://92f7c2826dd64df9311d4148674cf8b75efb9af0214c20ffa44217ea031261dd
    Image:         ghcr.io/openfaas/gateway:0.27.12
    Image ID:      ghcr.io/openfaas/gateway@sha256:fe62d9e15c4d8743f8049d83f5c2f82a28bd12ede7dd66e5c51261161adb5980
    Ports:         8080/TCP, 8082/TCP
    Host Ports:    0/TCP, 0/TCP
    Command:
      ./gateway
    State:          Waiting
      Reason:       CrashLoopBackOff
    Last State:     Terminated
      Reason:       Error
      Exit Code:    2
      Started:      Mon, 04 Aug 2025 15:34:05 +0800
      Finished:     Mon, 04 Aug 2025 15:34:20 +0800
    Ready:          False
    Restart Count:  63
    Requests:
      cpu:      100m
      memory:   120Mi
    Liveness:   http-get http://:8080/healthz delay=1s timeout=5s period=5s #success=1 #failure=3
    Readiness:  http-get http://:8080/healthz delay=1s timeout=5s period=5s #success=1 #failure=3
    Environment:
      read_timeout:             1m05s
      write_timeout:            1m05s
      upstream_timeout:         1m
      functions_provider_url:   http://127.0.0.1:8081/
      direct_functions:         false
      direct_functions_suffix:  openfaas-fn.svc.cluster.local
      function_namespace:       openfaas-fn
      faas_nats_address:        nats.openfaas.svc.cluster.local
      faas_nats_port:           4222
      faas_nats_channel:        faas-request
      basic_auth:               true
      secret_mount_path:        /var/secrets
      scale_from_zero:          true
      max_idle_conns:           1024
      max_idle_conns_per_host:  1024
      probe_functions:          false
      async:                    true
    Mounts:
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-jnthx (ro)
      /var/secrets from auth (ro)
  faas-netes:
    Container ID:  containerd://717ced1ac13673dc371556ea2f3f3842ea299041e0413c8b85b8b8c965bcf7a7
    Image:         koinikki/faas-netes:latest
    Image ID:      docker.io/koinikki/faas-netes@sha256:812891807eee6320b80f1ac5ad85f6cd1e665bd91be828342039b831bc65c193
    Port:          8081/TCP
    Host Port:     0/TCP
    Command:
      ./faas-netes
    State:          Waiting
      Reason:       CrashLoopBackOff
    Last State:     Terminated
      Reason:       Error
      Exit Code:    1
      Started:      Mon, 04 Aug 2025 15:34:06 +0800
      Finished:     Mon, 04 Aug 2025 15:34:16 +0800
    Ready:          False
    Restart Count:  61
    Requests:
      cpu:     100m
      memory:  120Mi
    Liveness:  http-get http://:8081/healthz delay=5s timeout=3s period=5s #success=1 #failure=3
    Environment:
      port:                                   8081
      function_namespace:                     openfaas-fn
      read_timeout:                           1m05s
      profiles_namespace:                     openfaas
      write_timeout:                          1m05s
      image_pull_policy:                      Always
      http_probe:                             true
      set_nonroot_user:                       false
      readiness_probe_initial_delay_seconds:  0
      readiness_probe_timeout_seconds:        1
      readiness_probe_period_seconds:         2
      readiness_probe_success_threshold:      1
      readiness_probe_failure_threshold:      3
      liveness_probe_initial_delay_seconds:   0
      liveness_probe_timeout_seconds:         1
      liveness_probe_period_seconds:          2
      liveness_probe_failure_threshold:       3
      cluster_role:                           false
      basic_auth:                             true
      secret_mount_path:                      /var/secrets
      debug:                                  false
      log_encoding:                           console
      http_proxy:                             http://*************:7897
      https_proxy:                            http://*************:7897
    Mounts:
      /tmp from faas-netes-temp-volume (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-jnthx (ro)
      /var/secrets from auth (ro)
Conditions:
  Type                        Status
  PodReadyToStartContainers   True 
  Initialized                 True 
  Ready                       False 
  ContainersReady             False 
  PodScheduled                True 
Volumes:
  faas-netes-temp-volume:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     
    SizeLimit:  <unset>
  auth:
    Type:        Secret (a volume populated by a Secret)
    SecretName:  basic-auth
    Optional:    false
  kube-api-access-jnthx:
    Type:                    Projected (a volume that contains injected data from multiple sources)
    TokenExpirationSeconds:  3607
    ConfigMapName:           kube-root-ca.crt
    Optional:                false
    DownwardAPI:             true
QoS Class:                   Burstable
Node-Selectors:              <none>
Tolerations:                 node.kubernetes.io/not-ready:NoExecute op=Exists for 300s
                             node.kubernetes.io/unreachable:NoExecute op=Exists for 300s
Events:
  Type     Reason   Age                     From     Message
  ----     ------   ----                    ----     -------
  Warning  BackOff  4m41s (x574 over 123m)  kubelet  Back-off restarting failed container gateway in pod gateway-64ccc4b899-n7gc8_openfaas(aba62686-847f-44b0-9d14-ce6f166e224d)
  Normal   Pulled   53s (x50 over 124m)     kubelet  Container image "ghcr.io/openfaas/gateway:0.27.12" already present on machine
