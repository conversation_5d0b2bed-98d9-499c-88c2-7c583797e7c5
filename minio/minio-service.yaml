apiVersion: v1  # 指定Kubernetes API的版本为v1
kind: Service  # 定义资源类型为Service，用于暴露应用程序
metadata:
  name: minio-service  # Service的名称为minio-service
  namespace: openfaas-fn  # 指定命名空间为 openfaas-fn，该Service将部署在这个命名空间下
spec:
  selector:
    app: minio  # 选择器，将Service与具有app=minio标签的Pod关联起来
  ports:
    - name: api  # 端口名称为api
      port: 9000  # Service暴露的端口号为9000
      targetPort: 9000  # 目标Pod的端口号为9000，即Service将流量转发到Pod的9000端口
      nodePort: 30900  # 当Service类型为NodePort时，通过节点的30900端口可以访问Service
    - name: console  # 端口名称为console
      port: 9090  # Service暴露的端口号为9090。（之前是9001，agent建议一致9090？）
      targetPort: 9090  # 目标Pod的端口号为9090，即Service将流量转发到Pod的9090端口
      nodePort: 30990  # 当Service类型为NodePort时，通过节点的30990端口可以访问Service
  type: NodePort  # Service的类型为NodePort，允许外部通过节点IP和指定的NodePort访问Service