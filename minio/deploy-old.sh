#!/bin/bash

# MinIO Kubernetes 部署脚本
echo "开始部署 MinIO 到 Kubernetes..."

# 检查 openfaas-fn 命名空间是否存在
if ! kubectl get namespace openfaas-fn &> /dev/null; then
    echo "创建 openfaas-fn 命名空间..."
    kubectl create namespace openfaas-fn
fi

# 按顺序部署资源
# echo "1. 部署 ConfigMap..."
# kubectl apply -f minio-configmap.yaml

echo "2. 部署 PersistentVolumeClaim..."
kubectl apply -f minio-pvc.yaml

# echo "3. 等待 PVC 就绪..."
# kubectl wait --for=condition=Bound pvc/minio-pvc -n openfaas-fn --timeout=60s

echo "4. 部署 Deployment..."
kubectl apply -f minio-deployment.yaml

echo "5. 部署 Service..."
kubectl apply -f minio-service.yaml

echo "等待 Pod 就绪..."
kubectl wait --for=condition=Ready pod -l app=minio -n openfaas-fn --timeout=120s

echo "部署完成！"
echo
echo "访问信息："
echo "- API 端点: http://localhost:30900"
echo "- Web 控制台: http://localhost:30990"
echo "- 默认用户名: ROOTNAME"
echo "- 默认密码: CHANGEME123"
echo
echo "查看状态: kubectl get all -n openfaas-fn -l app=minio" 