// handler.js
/**
 * 处理函数，接收 FunctionEvent 和 FunctionContext 对象。
 * @param {FunctionEvent} event - 包含请求信息的对象。
 * @param {FunctionContext} context - 包含响应信息的对象。
 */
// 02141712
function handleRequest(event, context, callback) {
    try {
        console.log("02192225");
        const data = JSON.parse(event.body);
        const response = {
            status: 'success',
            message: '请求处理成功',
            data: data
        };
        console.log('请求处理成功，返回响应:', response);
        callback(null, response);
    } catch (err) {
        const response = {
            status: 'error',
            message: `解析请求体时出错: ${err.message}`,
            data: err.message
        };
        console.error('请求处理出错:', err);
        callback(err, response);
    }
}

module.exports = handleRequest;
// 尚未理解context和callback的使用
