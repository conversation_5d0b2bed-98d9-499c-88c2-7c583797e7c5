version: 1.0 # 指定 YAML 文件的版本，当前通常使用 1.0。
provider:
  name: openfaas
  gateway: http://127.0.0.1:31112  # 替换为你的 OpenFaaS 网关的实际 URL

functions:
  my-openfaas-function:
    lang: node16 # 指定函数使用的编程语言和运行时环境
    handler: ./handler
    image: yang8miao/openfaas-cpsf-sample:latest  # 替换为你的 Docker 镜像的实际名称和标签
    environment:
      # 函数运行时的环境变量，可根据需要添加更多
      NODE_ENV: production
    labels:
      # 标签可用于分类和管理函数
      function: my-openfaas-function
      team: my-team
    annotations:
      # 注解可用于提供额外的元数据
      description: "A simple OpenFaaS function that echoes the input."
    limits:
      # 资源限制，可根据函数的实际需求调整
      memory: 128Mi
      cpu: 100m
    requests:
      # 资源请求，可根据函数的实际需求调整
      memory: 64Mi
      cpu: 50m
    secrets:
      # 如果函数需要访问机密信息，可在此处引用机密
      - my-secret