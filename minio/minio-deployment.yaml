apiVersion: apps/v1  # 指定Kubernetes API的版本为apps/v1
kind: Deployment  # 定义资源类型为Deployment，用于管理Pod的副本和滚动更新
metadata:
  name: minio-deployment  # Deployment的名称为minio-deployment
  namespace: openfaas-fn  # 指定命名空间为 openfaas-fn，该Deployment将部署在这个命名空间下
spec:
  replicas: 1  # 副本数量为1，表示将创建1个Pod
  selector:
    matchLabels:
      app: minio  # 选择器，将Deployment与具有app=minio标签的Pod关联起来
  template:
    metadata:
      labels:
        app: minio  # Pod的标签为app=minio
    spec:
      containers:
      - name: minio  # 容器的名称为minio
        image: quay.io/minio/minio:latest  # 容器使用的镜像为quay.io/minio/minio:latest
        args:
        - server  # Minio服务的启动命令参数，这里是server，表示以服务模式启动
        - /data  # Minio服务的数据存储目录为/data
        - --console-address  # Minio控制台的地址参数
        - ":9090"  # Minio控制台的地址为:9090
        resources:
          # requests: # 系统保证给这个容器至少 256MB 内存和 0.25 个 CPU 核心
          #   memory: "256Mi"
          #   cpu: "250m"
          # limits: # 容器最多只能用 512MB 内存和 0.5 个 CPU 核心，超过会被限制或终止
          #   memory: "512Mi"
          #   cpu: "500m"
        env:
        - name: MINIO_ROOT_USER  # 环境变量名称为MINIO_ROOT_USER
          value: "ROOTNAME"  # 环境变量的值为ROOTNAME，即Minio的根用户名为ROOTNAME
        - name: MINIO_ROOT_PASSWORD  # 环境变量名称为MINIO_ROOT_PASSWORD
          value: "CHANGEME123"  # 环境变量的值为CHANGEME123，即Minio的根密码为CHANGEME123
        ports:
        - containerPort: 9000  # 容器暴露的端口号为9000
        - containerPort: 9090  # 容器暴露的端口号为9090
        volumeMounts:
        - name: minio-storage  # 卷挂载的名称为minio-storage
          mountPath: /data  # 将卷挂载到容器内的/data目录
      volumes:
      - name: minio-storage  # 卷的名称为minio-storage
        persistentVolumeClaim:
          claimName: minio-pvc  # 引用的PersistentVolumeClaim的名称为minio-pvc