apiVersion: v1  # 指定Kubernetes API的版本为v1
kind: PersistentVolumeClaim  # 定义资源类型为PersistentVolumeClaim，用于请求持久化存储
metadata:
  name: minio-pvc  # PersistentVolumeClaim的名称为minio-pvc
  namespace: openfaas-fn  # 指定命名空间为 openfaas-fn，该PVC将部署在这个命名空间下
spec:
  accessModes:
    - ReadWriteOnce  # 访问模式为ReadWriteOnce，表示该PVC只能被单个节点以读写方式挂载
  resources:
    requests:
      storage: 10Gi  # 请求的存储容量为10Gi
  # storageClassName: standard  # 默认情况下Kubernetes会根据环境选择合适的存储类
  storageClassName: local-storage  # 使用本地存储类