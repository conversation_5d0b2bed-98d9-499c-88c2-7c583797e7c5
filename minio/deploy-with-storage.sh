#!/bin/bash

# MinIO Kubernetes 完整部署脚本（包含存储配置）
echo "开始部署 MinIO 到 Kubernetes（包含存储配置）..."

# 创建存储目录
echo "1. 创建本地存储目录..."
sudo mkdir -p /tmp/minio-data
sudo chmod 777 /tmp/minio-data

# 检查 openfaas-fn 命名空间是否存在
if ! kubectl get namespace openfaas-fn &> /dev/null; then
    echo "2. 创建 openfaas-fn 命名空间..."
    kubectl create namespace openfaas-fn
fi

# 部署存储配置
echo "3. 部署存储配置（StorageClass和PV）..."
kubectl apply -f local-storage.yaml

# 等待StorageClass创建完成
sleep 2

# 按顺序部署资源
echo "4. 部署 PersistentVolumeClaim..."
kubectl apply -f minio-pvc.yaml

# 不必等待PVC bound，因为使用了volumeBindingMode: WaitForFirstConsumer，这意味着PVC会等待有Pod使用它时才绑定

echo "6. 部署 Deployment..."
kubectl apply -f minio-deployment.yaml

echo "7. 部署 Service..."
kubectl apply -f minio-service.yaml

echo "8. 等待 Pod 就绪..."
kubectl wait --for=condition=Ready pod -l app=minio -n openfaas-fn --timeout=120s

echo "部署完成！"
echo
echo "访问信息："
echo "- API 端点: http://localhost:30900"
echo "- Web 控制台: http://localhost:30990"
echo "- 默认用户名: ROOTNAME"
echo "- 默认密码: CHANGEME123"
echo "- 数据存储位置: /tmp/minio-data"
echo
echo "查看状态: kubectl get all -n openfaas-fn -l app=minio"
echo "查看存储: kubectl get pv,pvc -n openfaas-fn" 