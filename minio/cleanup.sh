#!/bin/bash

# MinIO Kubernetes 清理脚本
echo "开始清理 MinIO 资源..."

# 删除所有相关资源
kubectl delete -f minio-service.yaml --ignore-not-found=true
kubectl delete -f minio-deployment.yaml --ignore-not-found=true
kubectl delete -f minio-pvc.yaml --ignore-not-found=true
kubectl delete -f minio-configmap.yaml --ignore-not-found=true
kubectl delete -f local-storage.yaml --ignore-not-found=true

# 清理本地存储目录
echo "清理本地存储目录..."
sudo rm -rf /tmp/minio-data

echo "清理完成！"
echo "如需删除整个命名空间，请运行: kubectl delete namespace openfaas-fn" 