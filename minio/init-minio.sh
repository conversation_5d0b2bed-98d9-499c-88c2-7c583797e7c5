#!/bin/bash

# MinIO 初始化脚本
echo "开始初始化 MinIO..."

# 等待MinIO服务就绪
echo "1. 等待 MinIO 服务就绪..."
kubectl wait --for=condition=Ready pod -l app=minio -n openfaas-fn --timeout=120s

# 获取Service的Cluster IP
echo "2. 获取 MinIO Service IP..."
SERVICE_IP=$(kubectl get svc minio-service -n openfaas-fn -o jsonpath='{.spec.clusterIP}')
echo "MinIO Service IP: $SERVICE_IP"


# 设置mc别名
echo "3. 配置 mc 客户端..."
mc alias set faas http://$SERVICE_IP:9000 ROOTNAME CHANGEME123

# 验证连接
echo "4. 验证 MinIO 连接..."
mc admin info faas

# 创建buckets并复制文件
echo "5. 创建 buckets 并复制文件..."

# 创建 yyc-input2 bucket 并复制 ini/data 文件夹
echo "  - 创建 yyc-input2 bucket..."
mc mb faas/yyc-input2
echo "  - 复制 ini/data 文件夹到 yyc-input2/data/..."
mc cp --recursive ini/data faas/yyc-input2/

# 创建 yyc-output2 bucket（空bucket）
echo "  - 创建 yyc-output2 bucket..."
mc mb faas/yyc-output2

# 创建 openfaas-cpsf-input-1 bucket 并复制 ini/app
echo "  - 创建 openfaas-cpsf-input-1 bucket..."
mc mb faas/openfaas-cpsf-input-1
echo "  - 复制 ini/app 文件夹到 openfaas-cpsf-input-1/app/..."
mc cp --recursive ini/app faas/openfaas-cpsf-input-1/

# 创建 openfaas-cpsf-stack-1 bucket 并复制 ini/app-simple 到 test1 目录
echo "  - 创建 openfaas-cpsf-stack-1 bucket..."
mc mb faas/openfaas-cpsf-stack-1
echo "  - 复制 ini/app-simple 文件到 openfaas-cpsf-stack-1/test1/..."
mc cp --recursive ini/app-simple/ faas/openfaas-cpsf-stack-1/test1/

# 显示所有buckets和内容
echo "6. 验证创建的 buckets:"
mc ls faas/

echo
echo "7. 验证每个bucket的内容:"
echo "yyc-input2:"
mc ls faas/yyc-input2/
echo "yyc-input2/data/:"
mc ls faas/yyc-input2/data/
echo
echo "yyc-output2:"
mc ls faas/yyc-output2/
echo
echo "openfaas-cpsf-input-1:"
mc ls faas/openfaas-cpsf-input-1/
echo "openfaas-cpsf-input-1/app/:"
mc ls faas/openfaas-cpsf-input-1/app/
echo
echo "openfaas-cpsf-stack-1:"
mc ls faas/openfaas-cpsf-stack-1/
echo "openfaas-cpsf-stack-1/test1/:"
mc ls faas/openfaas-cpsf-stack-1/test1/

echo
echo "MinIO 初始化完成！"
echo
echo "访问信息："
echo "- Web 控制台: http://localhost:30990"
echo "- API 端点: http://localhost:30900"
echo "- 集群内部 API: http://$SERVICE_IP:9000"
echo "- 用户名: ROOTNAME"
echo "- 密码: CHANGEME123" 