# MinIO Kubernetes 部署和使用指南

## 1. 部署 MinIO
- 特别注意：local-storage.yaml里面标注了节点，在更换服务器时需要修改？否则PersistentVolume的节点亲和性配置错误
### 方法一：使用部署脚本（推荐）
```bash
# 进入目录
cd yuchen/tools/minio

# 给脚本执行权限
chmod +x deploy-with-storage.sh init-minio.sh cleanup.sh

# 部署MinIO（包含存储配置）
./deploy-with-storage.sh

# 初始化MinIO（创建buckets并复制文件）
./init-minio.sh
```

**重要说明：**
- ✅ 使用 `deploy-with-storage.sh` - 包含完整存储配置，解决PVC Pending问题
- ❌ 不要使用 `deploy.sh` - 会遇到PVC Pending问题（缺少StorageClass）

### 方法二：手动部署
```bash
# 创建命名空间（如果不存在）
kubectl create namespace openfaas-fn

# 按顺序部署（注意：configmap可选）
kubectl apply -f minio-configmap.yaml  # 可选
kubectl apply -f minio-pvc.yaml
kubectl apply -f minio-deployment.yaml
kubectl apply -f minio-service.yaml

# 检查状态
kubectl get all -n openfaas-fn -l app=minio
```

## 2. 初始化后的Buckets结构

初始化脚本会自动创建以下buckets和目录结构：

1. **yyc-input2/data/** - 包含 `ini/data/` 下的CSV文件
   - yyc-input2/data/map1.csv
   - yyc-input2/data/map2.csv
   - yyc-input2/data/map3.csv
   - yyc-input2/data/map4.csv

2. **yyc-output2/** - 空bucket，用于输出

3. **openfaas-cpsf-input-1/app/** - 包含 `ini/app/` 下的所有文件
   - openfaas-cpsf-input-1/app/Dockerfile
   - openfaas-cpsf-input-1/app/handler.js
   - openfaas-cpsf-input-1/app/index.js
   - openfaas-cpsf-input-1/app/package.json
   - openfaas-cpsf-input-1/app/stack.yaml

4. **openfaas-cpsf-stack-1/test1/** - 包含 `ini/app-simple/` 下的文件
   - openfaas-cpsf-stack-1/test1/handler.js

## 3. 访问 MinIO

### Web 控制台
- **地址**: http://localhost:30990
- **用户名**: ROOTNAME  
- **密码**: CHANGEME123

### API 端点
- **地址**: http://localhost:30900

## 4. 安装和配置 MinIO Client (mc)

### 安装 mc
```bash
# 下载 mc
wget https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc
sudo mv mc /usr/local/bin/

# 或者使用包管理器
# Ubuntu/Debian
sudo apt install minio-client

# 验证安装
mc --version
```

### 配置 mc 连接到 MinIO
```bash
# 添加 MinIO 服务器配置
mc alias set myminio http://localhost:30900 ROOTNAME CHANGEME123

# 验证连接
mc admin info myminio

# 列出所有别名
mc alias list
```

## 5. 常用 mc 操作

### 存储桶操作
```bash
# 创建存储桶
mc mb myminio/mybucket

# 列出所有存储桶
mc ls myminio

# 删除存储桶（必须为空）
mc rb myminio/mybucket
```

### 文件操作
```bash
# 上传文件
mc cp localfile.txt myminio/mybucket/

# 上传整个目录
mc cp --recursive localdir/ myminio/mybucket/

# 下载文件
mc cp myminio/mybucket/file.txt ./

# 下载整个目录
mc cp --recursive myminio/mybucket/mydir/ ./

# 列出文件
mc ls myminio/mybucket/

# 删除文件
mc rm myminio/mybucket/file.txt

# 删除目录
mc rm --recursive myminio/mybucket/mydir/
```

### 文件同步
```bash
# 同步本地目录到 MinIO
mc mirror localdir/ myminio/mybucket/remotedir/

# 同步 MinIO 到本地
mc mirror myminio/mybucket/remotedir/ localdir/
```

### 权限和策略
```bash
# 设置存储桶为公开读取
mc anonymous set public myminio/mybucket

# 设置存储桶为私有
mc anonymous set none myminio/mybucket

# 生成预签名 URL（临时访问链接）
mc share download myminio/mybucket/file.txt --expire=24h
```

## 6. Serverless 应用连接到 MinIO

### Python 示例（使用 boto3）
```python
import boto3
from botocore.config import Config

# 配置 MinIO 客户端
s3_client = boto3.client(
    's3',
    endpoint_url='http://localhost:30900',  # MinIO API 端点
    aws_access_key_id='ROOTNAME',
    aws_secret_access_key='CHANGEME123',
    config=Config(signature_version='s3v4'),
    region_name='us-east-1'  # 任意区域名
)

# 创建存储桶
s3_client.create_bucket(Bucket='my-serverless-bucket')

# 上传文件
s3_client.upload_file('local_file.txt', 'my-serverless-bucket', 'uploaded_file.txt')

# 下载文件
s3_client.download_file('my-serverless-bucket', 'uploaded_file.txt', 'downloaded_file.txt')

# 列出对象
response = s3_client.list_objects_v2(Bucket='my-serverless-bucket')
for obj in response.get('Contents', []):
    print(obj['Key'])
```

### Node.js 示例（使用 AWS SDK）
```javascript
const AWS = require('aws-sdk');

// 配置 MinIO 客户端
const s3 = new AWS.S3({
    endpoint: 'http://localhost:30900',
    accessKeyId: 'ROOTNAME',
    secretAccessKey: 'CHANGEME123',
    s3ForcePathStyle: true,
    signatureVersion: 'v4'
});

// 创建存储桶
s3.createBucket({ Bucket: 'my-serverless-bucket' }, (err, data) => {
    if (err) console.log(err);
    else console.log('Bucket created successfully');
});

// 上传文件
const params = {
    Bucket: 'my-serverless-bucket',
    Key: 'my-file.txt',
    Body: 'Hello MinIO from Serverless!'
};

s3.upload(params, (err, data) => {
    if (err) console.log(err);
    else console.log('File uploaded successfully');
});
```

### 环境变量配置（推荐）
```bash
# 在 serverless 应用中使用环境变量
export MINIO_ENDPOINT=http://localhost:30900
export MINIO_ACCESS_KEY=ROOTNAME
export MINIO_SECRET_KEY=CHANGEME123
export MINIO_BUCKET=my-app-bucket
```

## 6. 在 Kubernetes 中的 Serverless 应用连接

### OpenFaaS 函数示例
```python
# 在同一命名空间中的函数可以使用内部服务名
import boto3

s3_client = boto3.client(
    's3',
    endpoint_url='http://minio-service:9000',  # 使用内部服务名
    aws_access_key_id='ROOTNAME',
    aws_secret_access_key='CHANGEME123'
)
```

## 7. 监控和管理

### 查看 MinIO 状态
```bash
# 查看 Pod 状态
kubectl get pods -n openfaas-fn -l app=minio

# 查看服务状态
kubectl get svc -n openfaas-fn -l app=minio

# 查看日志
kubectl logs -n openfaas-fn -l app=minio

# 进入容器
kubectl exec -it -n openfaas-fn deployment/minio-deployment -- /bin/sh
```

### 清理资源
```bash
# 使用清理脚本
./cleanup.sh

# 或手动清理
kubectl delete -f minio-service.yaml
kubectl delete -f minio-deployment.yaml
kubectl delete -f minio-pvc.yaml
kubectl delete -f minio-configmap.yaml
```

## 8. 生产环境建议

### 安全配置
- 修改默认用户名和密码
- 使用 Secret 管理敏感信息
- 启用 TLS/SSL
- 配置网络策略

### 高可用配置
- 增加副本数量
- 使用分布式模式
- 配置持久存储
- 设置资源限制和请求

### 示例 Secret 配置
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: minio-secret
  namespace: openfaas-fn
type: Opaque
stringData:
  MINIO_ROOT_USER: "your-username"
  MINIO_ROOT_PASSWORD: "your-secure-password"
```

## 故障排除

### 常见问题
1. **Pod 无法启动**: 检查 PVC 是否正确绑定
2. **无法访问服务**: 确认 NodePort 端口是否开放
3. **连接被拒绝**: 检查防火墙和网络配置
4. **存储空间不足**: 增加 PVC 存储容量

### 调试命令
```bash
# 检查事件
kubectl get events -n openfaas-fn

# 详细查看资源
kubectl describe pod -n openfaas-fn -l app=minio
kubectl describe pvc -n openfaas-fn minio-pvc
```

## 9. 故障排除和清理

### 如果部署失败
```bash
# 清理所有资源并重新部署
./cleanup.sh
./deploy-with-storage.sh
```

### 如果初始化失败
```bash
# 检查MinIO是否运行
kubectl get pods -n openfaas-fn -l app=minio

# 检查Service状态
kubectl get svc minio-service -n openfaas-fn

# 重新运行初始化
./init-minio.sh
```

### 完全清理
当需要完全清理所有资源时：
```bash
./cleanup.sh
```

这会删除：
- 所有Kubernetes资源（Service、Deployment、PVC、ConfigMap）
- StorageClass和PersistentVolume
- 本地存储目录 `/tmp/minio-data`
