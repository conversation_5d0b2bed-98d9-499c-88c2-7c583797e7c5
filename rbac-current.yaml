apiVersion: v1
items:
- apiVersion: rbac.authorization.k8s.io/v1
  kind: Role
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"rbac.authorization.k8s.io/v1","kind":"Role","metadata":{"annotations":{},"labels":{"app":"openfaas","chart":"openfaas-14.2.104","component":"faas-controller","heritage":"Helm","release":"openfaas"},"name":"openfaas-profiles","namespace":"openfaas"},"rules":[{"apiGroups":["openfaas.com"],"resources":["profiles","policies"],"verbs":["get","list","watch"]}]}
    creationTimestamp: "2025-06-13T13:24:38Z"
    labels:
      app: openfaas
      chart: openfaas-14.2.104
      component: faas-controller
      heritage: Helm
      release: openfaas
    name: openfaas-profiles
    namespace: openfaas
    resourceVersion: "1344"
    uid: 02255a32-0b0d-479c-982f-b4b231c54513
  rules:
  - apiGroups:
    - openfaas.com
    resources:
    - profiles
    - policies
    verbs:
    - get
    - list
    - watch
- apiVersion: rbac.authorization.k8s.io/v1
  kind: Role
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"rbac.authorization.k8s.io/v1","kind":"Role","metadata":{"annotations":{},"labels":{"app":"openfaas","chart":"openfaas-14.2.104","component":"prometheus","heritage":"Helm","release":"openfaas"},"name":"openfaas-prometheus","namespace":"openfaas"},"rules":[{"apiGroups":[""],"resources":["services","endpoints","pods"],"verbs":["get","list","watch"]}]}
    creationTimestamp: "2025-06-13T13:24:38Z"
    labels:
      app: openfaas
      chart: openfaas-14.2.104
      component: prometheus
      heritage: Helm
      release: openfaas
    name: openfaas-prometheus
    namespace: openfaas
    resourceVersion: "1345"
    uid: 7cdfde14-4597-49b3-88a0-c1f550fe6c9d
  rules:
  - apiGroups:
    - ""
    resources:
    - services
    - endpoints
    - pods
    verbs:
    - get
    - list
    - watch
- apiVersion: rbac.authorization.k8s.io/v1
  kind: RoleBinding
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"rbac.authorization.k8s.io/v1","kind":"RoleBinding","metadata":{"annotations":{},"labels":{"app":"openfaas","chart":"openfaas-14.2.104","component":"faas-controller","heritage":"Helm","release":"openfaas"},"name":"openfaas-profiles","namespace":"openfaas"},"roleRef":{"apiGroup":"rbac.authorization.k8s.io","kind":"Role","name":"openfaas-profiles"},"subjects":[{"kind":"ServiceAccount","name":"openfaas-controller","namespace":"openfaas"}]}
    creationTimestamp: "2025-06-13T13:24:38Z"
    labels:
      app: openfaas
      chart: openfaas-14.2.104
      component: faas-controller
      heritage: Helm
      release: openfaas
    name: openfaas-profiles
    namespace: openfaas
    resourceVersion: "1350"
    uid: fd41ea2a-9787-467a-a3b4-7de3432e2ce8
  roleRef:
    apiGroup: rbac.authorization.k8s.io
    kind: Role
    name: openfaas-profiles
  subjects:
  - kind: ServiceAccount
    name: openfaas-controller
    namespace: openfaas
- apiVersion: rbac.authorization.k8s.io/v1
  kind: RoleBinding
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"rbac.authorization.k8s.io/v1","kind":"RoleBinding","metadata":{"annotations":{},"labels":{"app":"openfaas","chart":"openfaas-14.2.104","component":"prometheus","heritage":"Helm","release":"openfaas"},"name":"openfaas-prometheus","namespace":"openfaas"},"roleRef":{"apiGroup":"rbac.authorization.k8s.io","kind":"Role","name":"openfaas-prometheus"},"subjects":[{"kind":"ServiceAccount","name":"openfaas-prometheus","namespace":"openfaas"}]}
    creationTimestamp: "2025-06-13T13:24:38Z"
    labels:
      app: openfaas
      chart: openfaas-14.2.104
      component: prometheus
      heritage: Helm
      release: openfaas
    name: openfaas-prometheus
    namespace: openfaas
    resourceVersion: "1351"
    uid: bd6b4d3f-2233-4b54-8112-4b51a2888367
  roleRef:
    apiGroup: rbac.authorization.k8s.io
    kind: Role
    name: openfaas-prometheus
  subjects:
  - kind: ServiceAccount
    name: openfaas-prometheus
    namespace: openfaas
kind: List
metadata:
  resourceVersion: ""
