#!/bin/bash

# OpenFaaS 彻底清理脚本
set -e

echo "🧹 开始彻底清理 OpenFaaS 资源..."

# 1. 删除 Helm Release
echo "📦 删除 Helm Release..."
helm uninstall openfaas -n openfaas --no-hooks 2>/dev/null || true
helm uninstall openfaas -n openfaas-fn --no-hooks 2>/dev/null || true

# 2. 删除命名空间
echo "🗂️ 删除命名空间..."
kubectl delete namespace openfaas --ignore-not-found=true --timeout=60s
kubectl delete namespace openfaas-fn --ignore-not-found=true --timeout=60s

# 3. 删除 CRD (Custom Resource Definitions)
echo "🔧 删除 OpenFaaS CRDs..."
kubectl get crd -o name | grep 'openfaas.com' | xargs -r kubectl delete

# 4. 删除集群范围资源
echo "🌐 删除集群范围资源..."
kubectl delete clusterrole,clusterrolebinding,serviceaccount -l app=openfaas --all-namespaces --ignore-not-found=true
kubectl delete configmap,secret -l app=openfaas --all-namespaces --ignore-not-found=true

# 5. 删除残留的 Finalizers
echo "🔧 清理残留 Finalizers..."
for ns in openfaas openfaas-fn; do
  if kubectl get namespace $ns &>/dev/null; then
    kubectl patch namespace $ns -p '{"metadata":{"finalizers":[]}}' --type=merge
  fi
done

# 6. 强制删除卡住的命名空间
echo "💥 强制删除卡住的命名空间..."
kubectl get namespace openfaas -o json | tr -d "\n" | sed "s/\"finalizers\": \[[^]]\+\]/\"finalizers\": []/" | kubectl replace --raw /api/v1/namespaces/openfaas/finalize -f - 2>/dev/null || true
kubectl get namespace openfaas-fn -o json | tr -d "\n" | sed "s/\"finalizers\": \[[^]]\+\]/\"finalizers\": []/" | kubectl replace --raw /api/v1/namespaces/openfaas-fn/finalize -f - 2>/dev/null || true

echo "⏳ 等待资源完全删除..."
sleep 10

# 验证清理结果
echo "🔍 验证清理结果..."

# 检查命名空间
if kubectl get namespace openfaas &>/dev/null || kubectl get namespace openfaas-fn &>/dev/null; then
    echo "❌ 错误: OpenFaaS 命名空间仍然存在"
    exit 1
else
    echo "✅ 命名空间已清理"
fi

# 检查 CRD
if kubectl get crd -o name | grep -q 'openfaas.com'; then
    echo "❌ 错误: OpenFaaS CRD 仍然存在"
    kubectl get crd -o name | grep 'openfaas.com'
    exit 1
else
    echo "✅ CRD 已清理"
fi

# 检查集群资源
if kubectl get clusterrole,clusterrolebinding,serviceaccount -l app=openfaas --all-namespaces -o name | grep -q 'openfaas'; then
    echo "❌ 错误: 集群资源仍然存在"
    kubectl get clusterrole,clusterrolebinding,serviceaccount -l app=openfaas --all-namespaces -o name
    exit 1
else
    echo "✅ 集群资源已清理"
fi

# 检查 Helm Release
if helm list --all-namespaces -o json | jq -r '.[] | select(.name=="openfaas")' | grep -q 'openfaas'; then
    echo "❌ 错误: Helm Release 仍然存在"
    helm list --all-namespaces | grep openfaas
    exit 1
else
    echo "✅ Helm Release 已清理"
fi

echo "🎉 OpenFaaS 已彻底清理完成！现在可以重新安装了。"

# # 安装
# kubectl create namespace openfaas-fn --dry-run=client -o yaml | kubectl apply -f -

# # 安装OpenFaaS
# helm install openfaas openfaas/openfaas \
# --namespace openfaas \
# --create-namespace \
# --set serviceType=NodePort \
# --set gateway.nodePort=31112 \
# --set basicAuth=true \
# --wait