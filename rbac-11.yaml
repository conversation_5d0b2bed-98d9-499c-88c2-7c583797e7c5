apiVersion: v1
items:
- apiVersion: rbac.authorization.k8s.io/v1
  kind: Role
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"rbac.authorization.k8s.io/v1","kind":"Role","metadata":{"annotations":{},"labels":{"app":"openfaas","chart":"openfaas-14.2.115","component":"faas-controller","heritage":"Helm","release":"openfaas"},"name":"openfaas-profiles","namespace":"openfaas"},"rules":[{"apiGroups":["openfaas.com"],"resources":["profiles","policies"],"verbs":["get","list","watch"]}]}
    creationTimestamp: "2025-07-31T10:34:45Z"
    labels:
      app: openfaas
      chart: openfaas-14.2.115
      component: faas-controller
      heritage: He<PERSON>
      release: openfaas
    name: openfaas-profiles
    namespace: openfaas
    resourceVersion: "7495"
    uid: 5f234aab-0502-4fcc-87c8-94b859dfdde3
  rules:
  - apiGroups:
    - openfaas.com
    resources:
    - profiles
    - policies
    verbs:
    - get
    - list
    - watch
- apiVersion: rbac.authorization.k8s.io/v1
  kind: Role
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"rbac.authorization.k8s.io/v1","kind":"Role","metadata":{"annotations":{},"labels":{"app":"openfaas","chart":"openfaas-14.2.115","component":"prometheus","heritage":"Helm","release":"openfaas"},"name":"openfaas-prometheus","namespace":"openfaas"},"rules":[{"apiGroups":[""],"resources":["services","endpoints","pods"],"verbs":["get","list","watch"]}]}
    creationTimestamp: "2025-07-31T10:34:45Z"
    labels:
      app: openfaas
      chart: openfaas-14.2.115
      component: prometheus
      heritage: Helm
      release: openfaas
    name: openfaas-prometheus
    namespace: openfaas
    resourceVersion: "7496"
    uid: ef908c4a-8250-497f-b9bf-a377195c18e1
  rules:
  - apiGroups:
    - ""
    resources:
    - services
    - endpoints
    - pods
    verbs:
    - get
    - list
    - watch
- apiVersion: rbac.authorization.k8s.io/v1
  kind: RoleBinding
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"rbac.authorization.k8s.io/v1","kind":"RoleBinding","metadata":{"annotations":{},"labels":{"app":"openfaas","chart":"openfaas-14.2.115","component":"faas-controller","heritage":"Helm","release":"openfaas"},"name":"openfaas-profiles","namespace":"openfaas"},"roleRef":{"apiGroup":"rbac.authorization.k8s.io","kind":"Role","name":"openfaas-profiles"},"subjects":[{"kind":"ServiceAccount","name":"openfaas-controller","namespace":"openfaas"}]}
    creationTimestamp: "2025-07-31T10:34:45Z"
    labels:
      app: openfaas
      chart: openfaas-14.2.115
      component: faas-controller
      heritage: Helm
      release: openfaas
    name: openfaas-profiles
    namespace: openfaas
    resourceVersion: "7499"
    uid: e23a5484-2d6c-4595-8b35-fff18350d23f
  roleRef:
    apiGroup: rbac.authorization.k8s.io
    kind: Role
    name: openfaas-profiles
  subjects:
  - kind: ServiceAccount
    name: openfaas-controller
    namespace: openfaas
- apiVersion: rbac.authorization.k8s.io/v1
  kind: RoleBinding
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"rbac.authorization.k8s.io/v1","kind":"RoleBinding","metadata":{"annotations":{},"labels":{"app":"openfaas","chart":"openfaas-14.2.115","component":"prometheus","heritage":"Helm","release":"openfaas"},"name":"openfaas-prometheus","namespace":"openfaas"},"roleRef":{"apiGroup":"rbac.authorization.k8s.io","kind":"Role","name":"openfaas-prometheus"},"subjects":[{"kind":"ServiceAccount","name":"openfaas-prometheus","namespace":"openfaas"}]}
    creationTimestamp: "2025-07-31T10:34:45Z"
    labels:
      app: openfaas
      chart: openfaas-14.2.115
      component: prometheus
      heritage: Helm
      release: openfaas
    name: openfaas-prometheus
    namespace: openfaas
    resourceVersion: "7500"
    uid: a68ad1c8-bb16-42e9-844d-597b6eb8f64d
  roleRef:
    apiGroup: rbac.authorization.k8s.io
    kind: Role
    name: openfaas-prometheus
  subjects:
  - kind: ServiceAccount
    name: openfaas-prometheus
    namespace: openfaas
kind: List
metadata:
  resourceVersion: ""
