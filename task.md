
OpenFaaS 部署超时问题技术报告
问题现象：faas-cli deploy 随机返回 400/502 错误，超时时间 30s/60s
影响范围：仅限异常服务器，同配置正常服务器部署成功
问题持续：已排除代理、Docker、K8s、OpenFaaS 配置差异

### 二、关键测试结果
1. 代理连通性测试
宿主机代理（通过）
curl -x http://127.0.0.1:7897 https://www.google.com -I → HTTP 200
Docker代理（通过）
docker run --rm alpine wget -O- https://httpbin.org/delay/5 → 成功
OpenFaaS网关代理（通过）
kubectl exec -it gateway -n openfaas -- wget -O- https://httpbin.org/delay/5 → 成功
2. 部署行为对比
操作	正常服务器	异常服务器	
faas-cli deploy	成功	❌ 随机 400/502 超时	
镜像拉取日志	无报错	无报错	
网关容器日志	无异常	无异常	
### 三、已排除的干扰项
代理软件差异：已统一使用 verge-mihomo 核心及相同配置文件
Docker守护进程配置：已同步 HTTP_PROXY/HTTPS_PROXY/NO_PROXY
网络策略：K8s 集群策略一致，无额外限制
资源限制：CPU/内存/磁盘空间充足
版本问题：faas-cli版本一致，openfaas部署方式一致（helm）
