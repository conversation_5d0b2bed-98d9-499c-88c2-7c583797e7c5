apiVersion: apps/v1
kind: ReplicaSet
metadata:
  annotations:
    deployment.kubernetes.io/desired-replicas: "1"
    deployment.kubernetes.io/max-replicas: "2"
    deployment.kubernetes.io/revision: "2"
  creationTimestamp: "2025-08-03T15:20:24Z"
  generation: 1
  labels:
    app: gateway
    pod-template-hash: 7cf796b557
  name: gateway-7cf796b557
  namespace: openfaas
  ownerReferences:
  - apiVersion: apps/v1
    blockOwnerDeletion: true
    controller: true
    kind: Deployment
    name: gateway
    uid: fc2bb541-2e6c-4365-9b01-4d718a38c581
  resourceVersion: "5683439"
  uid: f1a90d3c-a44c-4381-95f2-7ae63c2d1fe7
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gateway
      pod-template-hash: 7cf796b557
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/restartedAt: "2025-08-03T23:20:24+08:00"
        prometheus.io/port: "8082"
        prometheus.io/scrape: "true"
      creationTimestamp: null
      labels:
        app: gateway
        pod-template-hash: 7cf796b557
    spec:
      containers:
      - command:
        - ./gateway
        env:
        - name: read_timeout
          value: 1m05s
        - name: write_timeout
          value: 1m05s
        - name: upstream_timeout
          value: 1m
        - name: functions_provider_url
          value: http://127.0.0.1:8081/
        - name: direct_functions
          value: "false"
        - name: direct_functions_suffix
          value: openfaas-fn.svc.cluster.local
        - name: function_namespace
          value: openfaas-fn
        - name: faas_nats_address
          value: nats.openfaas.svc.cluster.local
        - name: faas_nats_port
          value: "4222"
        - name: faas_nats_channel
          value: faas-request
        - name: basic_auth
          value: "true"
        - name: secret_mount_path
          value: /var/secrets
        - name: scale_from_zero
          value: "true"
        - name: max_idle_conns
          value: "1024"
        - name: max_idle_conns_per_host
          value: "1024"
        - name: probe_functions
          value: "false"
        - name: async
          value: "true"
        image: ghcr.io/openfaas/gateway:0.27.12
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /healthz
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 1
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 5
        name: gateway
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        - containerPort: 8082
          name: gw-metrics
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /healthz
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 1
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 5
        resources:
          requests:
            cpu: 100m
            memory: 120Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /var/secrets
          name: auth
          readOnly: true
      - command:
        - ./faas-netes
        env:
        - name: port
          value: "8081"
        - name: function_namespace
          value: openfaas-fn
        - name: read_timeout
          value: 1m05s
        - name: profiles_namespace
          value: openfaas
        - name: write_timeout
          value: 1m05s
        - name: image_pull_policy
          value: Always
        - name: http_probe
          value: "true"
        - name: set_nonroot_user
          value: "false"
        - name: readiness_probe_initial_delay_seconds
          value: "0"
        - name: readiness_probe_timeout_seconds
          value: "1"
        - name: readiness_probe_period_seconds
          value: "2"
        - name: readiness_probe_success_threshold
          value: "1"
        - name: readiness_probe_failure_threshold
          value: "3"
        - name: liveness_probe_initial_delay_seconds
          value: "0"
        - name: liveness_probe_timeout_seconds
          value: "1"
        - name: liveness_probe_period_seconds
          value: "2"
        - name: liveness_probe_failure_threshold
          value: "3"
        - name: cluster_role
          value: "false"
        - name: basic_auth
          value: "true"
        - name: secret_mount_path
          value: /var/secrets
        - name: debug
          value: "false"
        - name: log_encoding
          value: console
        - name: http_proxy
          value: http://192.168.0.163:7897
        - name: https_proxy
          value: http://192.168.0.163:7897
        image: koinikki/faas-netes:latest
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /healthz
            port: 8081
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 3
        name: faas-netes
        ports:
        - containerPort: 8081
          name: provider
          protocol: TCP
        resources:
          requests:
            cpu: 100m
            memory: 120Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /var/secrets
          name: auth
          readOnly: true
        - mountPath: /tmp
          name: faas-netes-temp-volume
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: openfaas-controller
      serviceAccountName: openfaas-controller
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: faas-netes-temp-volume
      - name: auth
        secret:
          defaultMode: 420
          secretName: basic-auth
status:
  fullyLabeledReplicas: 1
  observedGeneration: 1
  replicas: 1
